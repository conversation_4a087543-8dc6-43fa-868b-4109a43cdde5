import 'package:flutter/material.dart';
import '../../models/tenant/tenant.dart';
import '../../models/property/property_model.dart';
import '../../models/room/room_model.dart';
import '../../models/activity_log.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import '../../widgets/app_loading_indicator.dart';
import '../../widgets/error_dialog.dart';
import '../tenants/add_tenant_page.dart'; // Import the AddTenantPage
// Import for tenant selection page
import 'tenant_selection_page.dart';

// Sort options for rooms
enum SortOption { nameAsc, nameDesc, priceAsc, priceDesc, typeAsc, typeDesc }

// Extension to get display name for sort options
extension SortOptionExtension on SortOption {
  String get displayName {
    switch (this) {
      case SortOption.nameAsc:
        return 'Name (A-Z)';
      case SortOption.nameDesc:
        return 'Name (Z-A)';
      case SortOption.priceAsc:
        return 'Price (Low-High)';
      case SortOption.priceDesc:
        return 'Price (High-Low)';
      case SortOption.typeAsc:
        return 'Type (A-Z)';
      case SortOption.typeDesc:
        return 'Type (Z-A)';
    }
  }
}

class AssignRoomPage extends StatefulWidget {
  final Tenant tenant;
  final String? preselectedRoomId;
  final String? preselectedRoomName;
  final bool isReactivation;

  const AssignRoomPage({
    super.key,
    required this.tenant,
    this.preselectedRoomId,
    this.preselectedRoomName,
    this.isReactivation = false,
  });

  @override
  State<AssignRoomPage> createState() => _AssignRoomPageState();
}

class _AssignRoomPageState extends State<AssignRoomPage> {
  bool _isLoading = true;
  String? _errorMessage;

  // Form data
  List<Property> _properties = [];
  Property? _selectedProperty;
  List<Room> _availableRooms = [];
  Room? _selectedRoom;

  // Search and sort
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  SortOption _sortBy = SortOption.nameAsc;

  // Optional fields
  final _leaseStartController = TextEditingController();
  final _leaseEndController = TextEditingController();
  final _depositAmountController = TextEditingController();
  final _notesController = TextEditingController();

  // Key for the form
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _loadProperties();
    _searchController.addListener(_onSearchChanged);

    // If we have a preselected room, we'll need to find it after loading properties
    if (widget.preselectedRoomId != null) {
      _loadPreselectedRoom();
    }
  }

  @override
  void dispose() {
    _leaseStartController.dispose();
    _leaseEndController.dispose();
    _depositAmountController.dispose();
    _notesController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  // Get filtered and sorted rooms
  List<Room> get _filteredAndSortedRooms {
    // Start with all available rooms
    List<Room> result = List.from(_availableRooms);

    // Apply search filter if query exists
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      result =
          result.where((room) {
            return room.name.toLowerCase().contains(query) ||
                room.roomTypeName.toLowerCase().contains(query) ||
                (room.description?.toLowerCase().contains(query) ?? false);
          }).toList();
    }

    // Apply sorting
    switch (_sortBy) {
      case SortOption.nameAsc:
        result.sort((a, b) => a.name.compareTo(b.name));
        break;
      case SortOption.nameDesc:
        result.sort((a, b) => b.name.compareTo(a.name));
        break;
      case SortOption.priceAsc:
        result.sort((a, b) => a.rentalPrice.compareTo(b.rentalPrice));
        break;
      case SortOption.priceDesc:
        result.sort((a, b) => b.rentalPrice.compareTo(a.rentalPrice));
        break;
      case SortOption.typeAsc:
        result.sort((a, b) => a.roomTypeName.compareTo(b.roomTypeName));
        break;
      case SortOption.typeDesc:
        result.sort((a, b) => b.roomTypeName.compareTo(a.roomTypeName));
        break;
    }

    return result;
  }

  // Load all properties
  Future<void> _loadProperties() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final properties =
          await serviceLocator.propertyService.getAllProperties();

      if (mounted) {
        setState(() {
          _properties = properties;
          _isLoading = false;
        });

        // If we have a preselected room, try to load it now that properties are loaded
        if (widget.preselectedRoomId != null) {
          _loadPreselectedRoom();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load properties: $e';
          _isLoading = false;
        });
      }
    }
  }

  // Load available rooms for the selected property
  Future<void> _loadAvailableRooms(String propertyId) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _selectedRoom = null;
    });

    try {
      final allRooms = await serviceLocator.roomService.getRoomsByPropertyId(
        propertyId,
      );

      // Filter to only vacant rooms, but include preselected room if it exists
      final availableRooms =
          allRooms
              .where(
                (room) =>
                    room.occupancyStatus == RoomOccupancyStatus.vacant ||
                    (widget.preselectedRoomId != null &&
                        room.id == widget.preselectedRoomId),
              )
              .toList();

      setState(() {
        _availableRooms = availableRooms;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load rooms: $e';
        _isLoading = false;
      });
    }
  }

  // Handle property selection change
  void _onPropertyChanged(Property? property) {
    setState(() {
      _selectedProperty = property;
      _availableRooms = [];
      _selectedRoom = null;
    });

    if (property != null) {
      _loadAvailableRooms(property.id);
    }
  }

  // Open date picker for lease start/end dates
  Future<void> _selectDate(
    BuildContext context,
    TextEditingController controller,
    String title,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
      helpText: title,
    );

    if (picked != null) {
      setState(() {
        controller.text =
            '${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}';
      });
    }
  }

  // Assign room to tenant
  Future<void> _assignRoom() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Capture context-dependent objects before async gaps.
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    if (_selectedRoom == null) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Please select a room'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // First verify the tenant still exists in the database
      final existingTenant = await serviceLocator.tenantService.getTenantById(
        widget.tenant.id,
      );
      if (existingTenant == null) {
        setState(() {
          _isLoading = false;
          _errorMessage =
              'Failed to assign room: Tenant not found with ID: ${widget.tenant.id}';
        });

        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'Tenant not found in the database. The record may have been deleted.',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'DISMISS',
              textColor: Colors.white,
              onPressed: () {},
            ),
          ),
        );
        return;
      }

      // For reactivation, try to fetch previous lease details if available
      DateTime? leaseStartDate;
      DateTime? leaseEndDate;

      if (widget.isReactivation ||
          widget.tenant.status == TenantStatus.movedOut) {
        // Use existing lease details if available
        if (_leaseStartController.text.isEmpty &&
            existingTenant.leaseStartDate != null) {
          leaseStartDate = existingTenant.leaseStartDate;
        } else if (_leaseStartController.text.isNotEmpty) {
          leaseStartDate = DateTime.parse(_leaseStartController.text);
        }

        if (_leaseEndController.text.isEmpty &&
            existingTenant.leaseEndDate != null) {
          leaseEndDate = existingTenant.leaseEndDate;
        } else if (_leaseEndController.text.isNotEmpty) {
          leaseEndDate = DateTime.parse(_leaseEndController.text);
        }
      } else {
        // For regular assignments, use the form values
        leaseStartDate =
            _leaseStartController.text.isNotEmpty
                ? DateTime.parse(_leaseStartController.text)
                : null;

        leaseEndDate =
            _leaseEndController.text.isNotEmpty
                ? DateTime.parse(_leaseEndController.text)
                : null;
      }

      // Update the tenant with the room ID and lease dates
      final updatedTenant = existingTenant.copyWith(
        roomId: _selectedRoom!.id,
        leaseStartDate: leaseStartDate,
        leaseEndDate: leaseEndDate,
        status:
            TenantStatus
                .active, // Always set status to active when assigning a room
        notes: _generateAssignmentNotes(),
      );

      // Update tenant in database
      await serviceLocator.tenantService.updateTenant(updatedTenant);

      // Update room occupancy status
      await serviceLocator.roomService.updateRoomOccupancyStatus(
        _selectedRoom!.id,
        RoomOccupancyStatus.occupied,
      );

      // Log the assignment in activity logs
      final wasMovedOut =
          widget.tenant.status == TenantStatus.movedOut ||
          widget.isReactivation;
      await serviceLocator.activityLogService.logActivity(
        ActivityLog(
          type: ActivityType.roomAssignment,
          tenantId: widget.tenant.id,
          roomId: _selectedRoom!.id,
          propertyId: _selectedProperty!.id,
          action:
              wasMovedOut
                  ? 'Tenant reactivated and room assigned'
                  : 'Room assigned to tenant',
          details: {
            'tenant_name':
                '${widget.tenant.firstName} ${widget.tenant.lastName}',
            'room_name': _selectedRoom!.name,
            'property_name': _selectedProperty!.name,
            'lease_start':
                leaseStartDate?.toString().split(' ')[0] ?? 'Not set',
            'lease_end': leaseEndDate?.toString().split(' ')[0] ?? 'Not set',
            'deposit_amount':
                _depositAmountController.text.isNotEmpty
                    ? CurrencyFormatter.formatAmount(
                      double.parse(_depositAmountController.text),
                    )
                    : '',
            'rental_price': CurrencyFormatter.formatAmount(
              _selectedRoom!.rentalPrice,
            ),
            'currency_code': CurrencyFormatter.getCurrencyCode(),
            'previous_status':
                wasMovedOut ? 'movedOut' : widget.tenant.status.name,
            'is_reactivation': wasMovedOut,
          },
        ),
      );

      // Show success message and close page
      if (mounted) {
        final wasMovedOut =
            widget.tenant.status == TenantStatus.movedOut ||
            widget.isReactivation;
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              wasMovedOut
                  ? 'Tenant reactivated and room assigned successfully'
                  : 'Room assigned successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );

        // Return updated tenant to previous screen
        navigator.pop(updatedTenant);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;

        // Provide more user-friendly error messages
        if (e.toString().contains('Tenant not found')) {
          _errorMessage =
              'Failed to assign room: Tenant no longer exists in the database';
        } else {
          _errorMessage = 'Failed to assign room: ${e.toString()}';
        }
      });

      // Show a more detailed error dialog
      if (mounted) {
        showDialog(
          context: navigator.context, // Use captured context
          builder:
              (context) => ErrorDialog(
                title: 'Error Assigning Room',
                message:
                    e.toString().contains('Tenant not found')
                        ? 'The tenant record could not be found in the database. It may have been deleted or modified.'
                        : 'There was an error while trying to assign the room:',
                details: e.toString(),
                onRetry: _assignRoom,
              ),
        );
      }
    }
  }

  // Generate notes for the assignment
  String _generateAssignmentNotes() {
    final existingNotes = widget.tenant.notes ?? '';
    final depositAmount =
        _depositAmountController.text.isNotEmpty
            ? 'Security Deposit: ${CurrencyFormatter.formatAmount(double.parse(_depositAmountController.text))}\n'
            : '';
    final customNotes =
        _notesController.text.isNotEmpty
            ? 'Assignment Notes: ${_notesController.text}\n'
            : '';

    final assignmentNote =
        '\n--- Room Assignment (${DateTime.now().toString().split('.')[0]}) ---\n'
        'Property: ${_selectedProperty?.name}\n'
        'Room: ${_selectedRoom?.name}\n'
        'Rental Price: ${CurrencyFormatter.formatAmount(_selectedRoom?.rentalPrice ?? 0)}\n'
        'Lease Start: ${_leaseStartController.text}\n'
        'Lease End: ${_leaseEndController.text}\n'
        '$depositAmount'
        '$customNotes';

    return existingNotes + assignmentNote;
  }

  // Load preselected room and its property
  Future<void> _loadPreselectedRoom() async {
    if (widget.preselectedRoomId == null) return;

    try {
      // Load the room details
      final room = await serviceLocator.roomService.getRoomById(
        widget.preselectedRoomId!,
      );

      if (room != null) {
        // Find the property this room belongs to
        for (final property in _properties) {
          if (property.id == room.propertyId) {
            // Select the property and load its rooms
            _onPropertyChanged(property);

            // After rooms are loaded, select the preselected room
            Future.delayed(const Duration(milliseconds: 300), () {
              if (mounted) {
                setState(() {
                  _selectedRoom = room;
                });
              }
            });

            break;
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading preselected room: $e');
    }
  }

  // Navigate to add tenant page
  Future<void> _navigateToAddTenant() async {
    // Capture context before async gap
    final navigatorContext = context;
    
    final newTenant = await Navigator.push<Tenant>(
      navigatorContext,
      MaterialPageRoute(
        builder: (context) => const AddTenantPage(),
      ),
    );

    // If a new tenant was created, update the current page with the new tenant
    if (newTenant != null && mounted) {
      // Close the current page and return to the previous page with the new tenant
      Navigator.pop(navigatorContext, newTenant);
    }
  }

  // Navigate to tenant selection page to switch tenant
  Future<void> _switchTenant() async {
    // Capture context before async gap
    final navigatorContext = context;
    
    try {
      // If a property is selected, we can pass its rooms
      final String? roomId = _selectedRoom?.id ?? widget.preselectedRoomId;
      final String? roomName = _selectedRoom?.name ?? widget.preselectedRoomName;
      
      final newTenant = await Navigator.push<Tenant>(
        navigatorContext,
        MaterialPageRoute(
          builder: (context) => TenantSelectionPage(
            roomId: roomId ?? '',
            roomName: roomName ?? '',
          ),
        ),
      );
      
      // If a new tenant was selected, update the current page
      if (newTenant != null && mounted) {
        // Navigate to a new AssignRoomPage with the new tenant
        final result = await Navigator.pushReplacement(
          navigatorContext,
          MaterialPageRoute(
            builder: (context) => AssignRoomPage(
              tenant: newTenant,
              preselectedRoomId: roomId,
              preselectedRoomName: roomName,
            ),
          ),
        );
        
        // Return the result to the previous screen if needed
        if (result != null && mounted) {
          Navigator.pop(navigatorContext, result);
        }
      }
    } catch (e) {
      // Show error dialog
      if (mounted) {
        showDialog(
          context: navigatorContext,
          builder: (context) => ErrorDialog(
            title: 'Error Switching Tenant',
            message: 'There was an error while trying to switch tenants:',
            details: e.toString(),
            onRetry: _switchTenant,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.isReactivation
              ? 'Reactivate Tenant & Assign Room'
              : 'Assign Room to ${widget.tenant.firstName} ${widget.tenant.lastName}',
        ),
        elevation: 2,
        // Show filter indicator if search is active
        actions: [
          // Add New Tenant button
          IconButton(
            icon: const Icon(Icons.person_add),
            tooltip: 'Add New Tenant',
            onPressed: _navigateToAddTenant,
          ),
          if (_searchQuery.isNotEmpty || _sortBy != SortOption.nameAsc)
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Chip(
                label: Text(
                  _searchQuery.isNotEmpty ? 'Filtered' : 'Sorted',
                  style: const TextStyle(fontSize: 12, color: Colors.white),
                ),
                backgroundColor: Colors.blue.shade700,
                visualDensity: VisualDensity.compact,
                padding: const EdgeInsets.symmetric(horizontal: 4),
              ),
            ),
        ],
      ),
      body:
          _isLoading
              ? const AppLoadingIndicator(message: 'Loading data...')
              : _errorMessage != null
              ? _buildErrorView()
              : _buildFormContent(),
    );
  }

  Widget _buildErrorView() {
    final bool isTenantNotFoundError =
        _errorMessage != null && _errorMessage!.contains('Tenant not found');

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isTenantNotFoundError ? Icons.person_off : Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              isTenantNotFoundError ? 'Tenant Not Found' : 'Error Loading Data',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              isTenantNotFoundError
                  ? 'The tenant record could not be found in the database. It may have been deleted or modified.'
                  : _errorMessage ?? 'An unknown error occurred',
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red, fontSize: 16),
            ),
          ),
          const SizedBox(height: 24),
          if (isTenantNotFoundError)
            ElevatedButton.icon(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back),
              label: const Text('Go Back'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            )
          else
            ElevatedButton.icon(
              onPressed: _loadProperties,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          if (isTenantNotFoundError) ...[
            const SizedBox(height: 16),
            const Text(
              'Please check if the tenant still exists in your database.',
              textAlign: TextAlign.center,
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFormContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Tenant Detail Card
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: _getTenantStatusColor().withValues(
                            alpha: 50.0, // 0.2 opacity equivalent
                            red: _getColorComponent((_getTenantStatusColor().red * 255).round()),
                            green: _getColorComponent((_getTenantStatusColor().green * 255).round()),
                            blue: _getColorComponent((_getTenantStatusColor().blue * 255).round()),
                          ),
                          radius: 24,
                          child: Text(
                            _getInitials(widget.tenant.firstName, widget.tenant.lastName),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _getTenantStatusColor(),
                              fontSize: 16,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${widget.tenant.firstName} ${widget.tenant.lastName}',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: _getTenantStatusColor().withValues(
                                        alpha: 25.0, // 0.1 opacity equivalent
                                        red: _getColorComponent((_getTenantStatusColor().red * 255).round()),
                                        green: _getColorComponent((_getTenantStatusColor().green * 255).round()),
                                        blue: _getColorComponent((_getTenantStatusColor().blue * 255).round()),
                                      ),
                                      borderRadius: BorderRadius.circular(4),
                                      border: Border.all(
                                        color: _getTenantStatusColor().withValues(
                                          alpha: 127.0, // 0.5 opacity equivalent
                                          red: _getColorComponent((_getTenantStatusColor().red * 255).round()),
                                          green: _getColorComponent((_getTenantStatusColor().green * 255).round()),
                                          blue: _getColorComponent((_getTenantStatusColor().blue * 255).round()),
                                        ),
                                      ),
                                    ),
                                    child: Text(
                                      _getTenantStatusText(),
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                        color: _getTenantStatusColor(),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        // Switch tenant button
                        ElevatedButton.icon(
                          onPressed: _switchTenant,
                          icon: const Icon(Icons.swap_horiz, size: 16),
                          label: const Text('Switch'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            backgroundColor: Colors.blue.shade700,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 8),
                    // Contact Information
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildInfoRow(
                                Icons.email_outlined,
                                'Email',
                                widget.tenant.email,
                              ),
                              const SizedBox(height: 8),
                              _buildInfoRow(
                                Icons.phone_outlined,
                                'Phone',
                                widget.tenant.phoneNumber ?? 'Not provided',
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (widget.tenant.emergencyContactName != null)
                                _buildInfoRow(
                                  Icons.contact_emergency_outlined,
                                  'Emergency Contact',
                                  widget.tenant.emergencyContactName!,
                                ),
                              if (widget.tenant.emergencyContactPhone != null)
                                const SizedBox(height: 8),
                              if (widget.tenant.emergencyContactPhone != null)
                                _buildInfoRow(
                                  Icons.phone_callback_outlined,
                                  'Emergency Phone',
                                  widget.tenant.emergencyContactPhone!,
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    if (widget.tenant.roomId != null) ...[
                      const SizedBox(height: 8),
                      const Divider(),
                      const SizedBox(height: 8),
                      _buildInfoRow(
                        Icons.warning_amber_rounded,
                        'Current Room',
                        'Tenant already has a room assigned',
                        textColor: Colors.orange,
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Show reactivation notice if applicable
            if (widget.isReactivation ||
                widget.tenant.status == TenantStatus.movedOut)
              Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'Tenant Reactivation',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'This tenant was previously marked as moved out. Assigning a room will reactivate the tenant and change their status to active.',
                      style: TextStyle(color: Colors.blue.shade900),
                    ),
                  ],
                ),
              ),

            // Property selection
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Select Property',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<Property>(
                      decoration: InputDecoration(
                        labelText: 'Property',
                        hintText: 'Select a property to view available rooms',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        prefixIcon: const Icon(Icons.apartment),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                      ),
                      value: _selectedProperty,
                      isExpanded: true,
                      icon: const Icon(Icons.arrow_drop_down_circle),
                      items:
                          _properties.map((property) {
                            return DropdownMenuItem<Property>(
                              value: property,
                              child: Text(
                                property.name,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            );
                          }).toList(),
                      onChanged: _onPropertyChanged,
                      validator: (value) {
                        if (value == null) {
                          return 'Please select a property';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Section divider
            if (_selectedProperty != null)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Row(
                  children: [
                    const Icon(Icons.arrow_downward, color: Colors.green),
                    const SizedBox(width: 8),
                    Text(
                      'Available Rooms in ${_selectedProperty!.name}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 8),

            // Room selection (only show if property is selected)
            if (_selectedProperty != null)
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.meeting_room,
                            color: Colors.blue,
                            size: 18,
                          ),
                          const SizedBox(width: 6),
                          const Text(
                            'Select Room',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (_selectedRoom != null) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green.shade100,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.check_circle,
                                    color: Colors.green,
                                    size: 12,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    _selectedRoom!.name,
                                    style: const TextStyle(
                                      color: Colors.green,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 12),

                      // Show message if no rooms available
                      if (_availableRooms.isEmpty)
                        const Padding(
                          padding: EdgeInsets.symmetric(vertical: 16.0),
                          child: Text(
                            'No vacant rooms available in this property',
                            style: TextStyle(color: Colors.red),
                          ),
                        )
                      else
                        Column(
                          children: [
                            // Search and sort bar
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Search field
                                Expanded(
                                  flex: 2,
                                  child: TextField(
                                    controller: _searchController,
                                    decoration: InputDecoration(
                                      hintText: 'Search rooms...',
                                      prefixIcon: const Icon(
                                        Icons.search,
                                        size: 20,
                                      ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                            vertical: 0,
                                            horizontal: 8,
                                          ),
                                      isDense: true,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                // Sort dropdown
                                Expanded(
                                  flex: 1,
                                  child: DropdownButtonFormField<SortOption>(
                                    value: _sortBy,
                                    decoration: InputDecoration(
                                      labelText: 'Sort',
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      isDense: true,
                                    ),
                                    icon: const Icon(
                                      Icons.arrow_drop_down,
                                      size: 20,
                                    ),
                                    isExpanded: true,
                                    itemHeight: 48,
                                    menuMaxHeight: 250,
                                    dropdownColor: Colors.white,
                                    alignment: Alignment.centerLeft,
                                    items:
                                        SortOption.values.map((option) {
                                          return DropdownMenuItem<SortOption>(
                                            value: option,
                                            child: Text(
                                              option.displayName,
                                              style: const TextStyle(
                                                fontSize: 12,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                            ),
                                          );
                                        }).toList(),
                                    onChanged: (value) {
                                      if (value != null) {
                                        setState(() {
                                          _sortBy = value;
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 16),

                            // Results info
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  '${_filteredAndSortedRooms.length} of ${_availableRooms.length} rooms',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade700,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                                if (_searchQuery.isNotEmpty)
                                  TextButton.icon(
                                    onPressed: () {
                                      _searchController.clear();
                                    },
                                    icon: const Icon(Icons.clear, size: 14),
                                    label: const Text(
                                      'Clear',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      minimumSize: const Size(0, 0),
                                      tapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                    ),
                                  ),
                              ],
                            ),

                            const SizedBox(height: 8),

                            // No results message
                            if (_filteredAndSortedRooms.isEmpty &&
                                _searchQuery.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12.0,
                                ),
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.search_off,
                                      size: 36,
                                      color: Colors.grey.shade400,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'No matches for "${_searchQuery}"',
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: Colors.grey.shade700,
                                      ),
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        _searchController.clear();
                                      },
                                      style: TextButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 4,
                                        ),
                                        minimumSize: const Size(0, 0),
                                        tapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                      ),
                                      child: const Text(
                                        'Clear search',
                                        style: TextStyle(fontSize: 12),
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                            // Room list
                            if (_filteredAndSortedRooms.isNotEmpty)
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: _filteredAndSortedRooms.length,
                                itemBuilder: (context, index) {
                                  final room = _filteredAndSortedRooms[index];
                                  final isSelected =
                                      _selectedRoom?.id == room.id;

                                  return Card(
                                    color:
                                        isSelected ? Colors.blue.shade50 : null,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      side: BorderSide(
                                        color:
                                            isSelected
                                                ? Colors.blue
                                                : Colors.grey.shade300,
                                        width: isSelected ? 2 : 1,
                                      ),
                                    ),
                                    elevation: isSelected ? 3 : 1,
                                    margin: const EdgeInsets.symmetric(
                                      vertical: 6,
                                    ),
                                    child: InkWell(
                                      onTap: () {
                                        setState(() {
                                          _selectedRoom = room;
                                        });
                                      },
                                      borderRadius: BorderRadius.circular(8),
                                      child: Padding(
                                        padding: const EdgeInsets.all(12.0),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            // Room name and price
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    room.name,
                                                    style: const TextStyle(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                                Container(
                                                  padding:
                                                      const EdgeInsets.symmetric(
                                                        horizontal: 8,
                                                        vertical: 2,
                                                      ),
                                                  decoration: BoxDecoration(
                                                    color: Colors.green.shade50,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          4,
                                                        ),
                                                    border: Border.all(
                                                      color:
                                                          Colors.green.shade100,
                                                    ),
                                                  ),
                                                  child: Text(
                                                    '${CurrencyFormatter.formatAmount(room.rentalPrice)}/month',
                                                    style: TextStyle(
                                                      fontSize: 13,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color:
                                                          Colors.green.shade700,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 8),

                                            // Room details
                                            Row(
                                              children: [
                                                // Left column - Type & Floor
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          Text(
                                                            'Type: ',
                                                            style: TextStyle(
                                                              fontSize: 13,
                                                              color:
                                                                  Colors
                                                                      .grey
                                                                      .shade700,
                                                            ),
                                                          ),
                                                          Text(
                                                            room.roomTypeName,
                                                            style:
                                                                const TextStyle(
                                                                  fontSize: 13,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                ),
                                                          ),
                                                        ],
                                                      ),
                                                      if (room.floor != null)
                                                        Row(
                                                          children: [
                                                            Text(
                                                              'Floor: ',
                                                              style: TextStyle(
                                                                fontSize: 13,
                                                                color:
                                                                    Colors
                                                                        .grey
                                                                        .shade700,
                                                              ),
                                                            ),
                                                            Text(
                                                              '${room.floor}',
                                                              style: const TextStyle(
                                                                fontSize: 13,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                    ],
                                                  ),
                                                ),

                                                // Right column - Size & Furnished
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      if (room.size != null)
                                                        Row(
                                                          children: [
                                                            Text(
                                                              'Size: ',
                                                              style: TextStyle(
                                                                fontSize: 13,
                                                                color:
                                                                    Colors
                                                                        .grey
                                                                        .shade700,
                                                              ),
                                                            ),
                                                            Text(
                                                              '${room.size} sq ft',
                                                              style: const TextStyle(
                                                                fontSize: 13,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      Row(
                                                        children: [
                                                          Text(
                                                            'Furnished: ',
                                                            style: TextStyle(
                                                              fontSize: 13,
                                                              color:
                                                                  Colors
                                                                      .grey
                                                                      .shade700,
                                                            ),
                                                          ),
                                                          Text(
                                                            room.isFurnished
                                                                ? 'Yes'
                                                                : 'No',
                                                            style:
                                                                const TextStyle(
                                                                  fontSize: 13,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),

                                            // Amenities
                                            if (room.amenities.isNotEmpty) ...[
                                              const SizedBox(height: 8),
                                              Wrap(
                                                spacing: 4,
                                                runSpacing: 4,
                                                children:
                                                    room.amenities.map((
                                                      amenity,
                                                    ) {
                                                      final amenityName =
                                                          amenity
                                                              .amenity
                                                              ?.name ??
                                                          amenity
                                                              .customAmenityName ??
                                                          'Amenity';
                                                      return Container(
                                                        padding:
                                                            const EdgeInsets.symmetric(
                                                              horizontal: 8,
                                                              vertical: 2,
                                                            ),
                                                        decoration: BoxDecoration(
                                                          color:
                                                              Colors
                                                                  .blue
                                                                  .shade50,
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                4,
                                                              ),
                                                        ),
                                                        child: Text(
                                                          amenityName,
                                                          style: TextStyle(
                                                            fontSize: 11,
                                                            color:
                                                                Colors
                                                                    .blue
                                                                    .shade800,
                                                          ),
                                                        ),
                                                      );
                                                    }).toList(),
                                              ),
                                            ],

                                            // Description (if available)
                                            if (room.description != null &&
                                                room
                                                    .description!
                                                    .isNotEmpty) ...[
                                              const SizedBox(height: 8),
                                              Text(
                                                room.description!,
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.grey.shade700,
                                                ),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ],

                                            // Select button
                                            Align(
                                              alignment: Alignment.centerRight,
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                  top: 8.0,
                                                ),
                                                child: ElevatedButton(
                                                  onPressed: () {
                                                    setState(() {
                                                      _selectedRoom = room;
                                                    });
                                                  },
                                                  style: ElevatedButton.styleFrom(
                                                    backgroundColor:
                                                        isSelected
                                                            ? Colors.green
                                                            : Theme.of(
                                                              context,
                                                            ).primaryColor,
                                                    foregroundColor:
                                                        Colors.white,
                                                    shape: RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            8,
                                                          ),
                                                    ),
                                                    padding:
                                                        const EdgeInsets.symmetric(
                                                          horizontal: 12,
                                                          vertical: 6,
                                                        ),
                                                    minimumSize: const Size(
                                                      0,
                                                      0,
                                                    ),
                                                    tapTargetSize:
                                                        MaterialTapTargetSize
                                                            .shrinkWrap,
                                                  ),
                                                  child: Text(
                                                    isSelected
                                                        ? 'Selected ✓'
                                                        : 'Select Room',
                                                    style: const TextStyle(
                                                      fontSize: 13,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                          ],
                        ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // Optional details
            if (_selectedRoom != null)
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Lease Details',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Lease start date
                      TextFormField(
                        controller: _leaseStartController,
                        decoration: InputDecoration(
                          labelText:
                              (widget.isReactivation ||
                                      widget.tenant.status ==
                                          TenantStatus.movedOut)
                                  ? 'Lease Start Date (Optional)'
                                  : 'Lease Start Date',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.calendar_today),
                            onPressed:
                                () => _selectDate(
                                  context,
                                  _leaseStartController,
                                  'Select Lease Start Date',
                                ),
                          ),
                        ),
                        readOnly: true,
                      ),

                      const SizedBox(height: 16),

                      // Lease end date
                      TextFormField(
                        controller: _leaseEndController,
                        decoration: InputDecoration(
                          labelText:
                              (widget.isReactivation ||
                                      widget.tenant.status ==
                                          TenantStatus.movedOut)
                                  ? 'Lease End Date (Optional)'
                                  : 'Lease End Date',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.calendar_today),
                            onPressed:
                                () => _selectDate(
                                  context,
                                  _leaseEndController,
                                  'Select Lease End Date',
                                ),
                          ),
                        ),
                        readOnly: true,
                        validator: (value) {
                          // Skip validation for reactivated tenants if fields are empty
                          if ((widget.isReactivation ||
                                  widget.tenant.status ==
                                      TenantStatus.movedOut) &&
                              (_leaseStartController.text.isEmpty ||
                                  (value != null && value.isEmpty))) {
                            return null;
                          }

                          if (_leaseStartController.text.isNotEmpty &&
                              value != null &&
                              value.isEmpty) {
                            return 'Please enter lease end date';
                          }
                          if (_leaseStartController.text.isNotEmpty &&
                              value != null &&
                              value.isNotEmpty) {
                            final start = DateTime.parse(
                              _leaseStartController.text,
                            );
                            final end = DateTime.parse(value);
                            if (end.isBefore(start)) {
                              return 'End date must be after start date';
                            }
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Security deposit
                      TextFormField(
                        controller: _depositAmountController,
                        decoration: InputDecoration(
                          labelText: 'Security Deposit Amount',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          prefixText:
                              '${CurrencyFormatter.getCurrencySymbol()} ',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Notes
                      TextFormField(
                        controller: _notesController,
                        decoration: InputDecoration(
                          labelText: 'Additional Notes',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // Submit button
            ElevatedButton.icon(
              onPressed: _selectedRoom != null ? _assignRoom : null,
              icon: const Icon(Icons.check),
              label: const Text('Confirm Assignment'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to get tenant status color
  Color _getTenantStatusColor() {
    switch (widget.tenant.status) {
      case TenantStatus.active:
        return Colors.green;
      case TenantStatus.pending:
        return Colors.orange;
      case TenantStatus.movedOut:
        return Colors.grey;
    }
  }

  // Helper method to get tenant status text
  String _getTenantStatusText() {
    switch (widget.tenant.status) {
      case TenantStatus.active:
        return 'Active';
      case TenantStatus.pending:
        return 'Pending';
      case TenantStatus.movedOut:
        return 'Moved Out';
    }
  }

  // Helper method to get color components safely
  double _getColorComponent(int component) {
    return component.toDouble();
  }

  // Helper method to get initials from name
  String _getInitials(String firstName, String lastName) {
    String firstInitial = firstName.isNotEmpty ? firstName[0].toUpperCase() : '';
    String lastInitial = lastName.isNotEmpty ? lastName[0].toUpperCase() : '';
    return '$firstInitial$lastInitial';
  }

  // Helper method to build info row
  Widget _buildInfoRow(IconData icon, String label, String value, {Color? textColor}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey.shade600,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: textColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
